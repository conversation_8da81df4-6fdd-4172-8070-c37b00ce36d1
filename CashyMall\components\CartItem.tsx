import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';

interface CartItemProps {
  id: number;
  name: string;
  price: number;
  quantity: number;
  imageUrl: string;
  size?: string;
  color?: string;
  onRemove: (id : number) => void;
  onUpdateQuantity: (id : number, quantity: number, size?: string, color?: string) => void;
}

export default function CartItem({
  id,
  name,
  price,
  quantity,
  imageUrl,
  size,
  color,
  onRemove,
  onUpdateQuantity,
}: CartItemProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Helper function to get color value for display
  const getColorValue = (colorName: string) => {
    const colorMap: { [key: string]: string } = {
      'red': '#FF0000',
      'blue': '#0000FF',
      'green': '#008000',
      'yellow': '#FFFF00',
      'orange': '#FFA500',
      'purple': '#800080',
      'pink': '#FFC0CB',
      'brown': '#A52A2A',
      'black': '#000000',
      'white': '#FFFFFF',
      'gray': '#808080',
      'grey': '#808080',
      'navy': '#000080',
      'maroon': '#800000',
      'olive': '#808000',
      'lime': '#00FF00',
      'aqua': '#00FFFF',
      'teal': '#008080',
      'silver': '#C0C0C0',
      'fuchsia': '#FF00FF',
    };

    return colorMap[colorName.toLowerCase()] || colorName.toLowerCase();
  };

  const handleIncrement = () => {
    onUpdateQuantity(id, quantity + 1, size, color);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      onUpdateQuantity(id, quantity - 1, size, color);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.cardBackground }]}>
      <Image
        source={{ uri: imageUrl }}
        style={styles.image}
        resizeMode="cover"
      />

      <View style={styles.contentContainer}>
        <View style={styles.infoContainer}>
          <Text
            style={[styles.name, { color: colors.text }]}
            numberOfLines={2}
          >
            {name}
          </Text>

          {/* Size and Color Information */}
          {(size || color) && (
            <View style={styles.variantsContainer}>
              {size && (
                <View style={[styles.variantChip, { backgroundColor: colors.background, borderColor: colors.border }]}>
                  <Ionicons name="resize-outline" size={12} color={colors.tabIconDefault} />
                  <Text style={[styles.variantText, { color: colors.text }]}>
                    {size}
                  </Text>
                </View>
              )}
              {color && (
                <View style={[styles.variantChip, { backgroundColor: colors.background, borderColor: colors.border }]}>
                  <View style={[
                    styles.colorIndicator,
                    {
                      backgroundColor: getColorValue(color),
                      borderColor: color.toLowerCase() === 'white' ? colors.border : 'rgba(0,0,0,0.1)'
                    }
                  ]} />
                  <Text style={[styles.variantText, { color: colors.text }]}>
                    {color}
                  </Text>
                </View>
              )}
            </View>
          )}

          <Text style={[styles.price, { color: colors.primary }]}>
            ${price.toFixed(2)}
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => onRemove(id)}
          >
            <Ionicons
              name="trash-outline"
              size={18}
              color={colors.error}
            />
          </TouchableOpacity>

          <View style={styles.quantityContainer}>
            <TouchableOpacity
              style={[styles.quantityButton, { borderColor: colors.border }]}
              onPress={handleDecrement}
              disabled={quantity <= 1}
            >
              <Ionicons
                name="remove"
                size={16}
                color={quantity <= 1 ? colors.tabIconDefault : colors.text}
              />
            </TouchableOpacity>

            <Text style={[styles.quantity, { color: colors.text }]}>
              {quantity}
            </Text>

            <TouchableOpacity
              style={[styles.quantityButton, { borderColor: colors.border }]}
              onPress={handleIncrement}
            >
              <Ionicons
                name="add"
                size={16}
                color={colors.text}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    marginBottom: Layout.spacing.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  image: {
    width: 100,
    height: 120,
  },
  contentContainer: {
    flex: 1,
    padding: Layout.spacing.sm,
    justifyContent: 'space-between',
  },
  infoContainer: {
    flex: 1,
  },
  name: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  variantsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: Layout.spacing.xs,
    gap: Layout.spacing.xs,
  },
  variantChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
    borderWidth: 1,
  },
  variantText: {
    fontSize: 11,
    fontWeight: '500',
    marginLeft: 4,
  },
  colorIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Layout.spacing.sm,
  },
  removeButton: {
    padding: Layout.spacing.xs,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: Layout.borderRadius.sm,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantity: {
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: Layout.spacing.sm,
  },
});
