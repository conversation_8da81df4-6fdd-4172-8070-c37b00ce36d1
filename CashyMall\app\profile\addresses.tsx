import React, { useContext, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  ScrollView,
  Platform,
  Alert,
  Modal,
  KeyboardAvoidingView
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import Input from '../../components/Input';
import Button from '../../components/Button';
import { AuthContext } from '@/context/AuthContext';
import { updateAddress, getUserProfile } from '../../services/api/auth';
import { saveToken, getToken } from '../../services/storage/tokenStorage';


export default function AddressesScreen() {
  const { user, refreshUser } = useContext(AuthContext)!;

  // Create address from user profile data
  const userAddress = user ? {
    id: user.id || '',
    name: `${user.firstname || user.firstName || ''} ${user.lastname || user.lastName || ''}`.trim() || 'No Name',
    street: user.address || '',
    city: user.city || '',
    state: user.state || '',
    zipCode: user.zipCode || '',
    country: user.country || '',
    phone: user.phoneNumber || '',
    isDefault: true
  } : null;

  const addresses = userAddress ? [userAddress] : [];
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState<typeof addresses[0] | null>(null);
  const [street, setStreet] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [country, setCountry] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleEditAddress = (address: typeof addresses[0]) => {
    setEditingAddress(address);
    setStreet(address.street);
    setCity(address.city);
    setState(address.state);
    setZipCode(address.zipCode);
    setCountry(address.country);
    setModalVisible(true);
  };



  const validateForm = () => {
    if (!street.trim()) {
      Alert.alert('Validation Error', 'Please enter a street address');
      return false;
    }
    if (!city.trim()) {
      Alert.alert('Validation Error', 'Please enter a city');
      return false;
    }
    if (!state.trim()) {
      Alert.alert('Validation Error', 'Please enter a state/province');
      return false;
    }
    if (!zipCode.trim()) {
      Alert.alert('Validation Error', 'Please enter a ZIP code');
      return false;
    }
    if (!country.trim()) {
      Alert.alert('Validation Error', 'Please enter a country');
      return false;
    }
    return true;
  };

  const handleSaveAddress = async () => {
    if (!validateForm()) {
      return;
    }

    if (!user?.id) {
      Alert.alert('Error', 'User information not available. Please try logging in again.');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare address data for API call
      const addressData = {
        address: street.trim(),
        city: city.trim(),
        state: state.trim(),
        zipCode: zipCode.trim(),
        country: country.trim(),
      };

      console.log('Updating address for user:', user.id);
      console.log('Address data:', addressData);

      // Call the update address API
      const response = await updateAddress( addressData);
      console.log('Update response:', response);

      if (response.status === 200 || response.data) {
        // Fetch the updated user profile to get fresh data
        try {
          const profileResponse = await getUserProfile(user.id);
          console.log('user',profileResponse.data)
          console.log('user',profileResponse.data?.data)
          if (profileResponse.data) {
            // Update storage with fresh data from API
            const currentAuth = await getToken();
            if (currentAuth) {
              await saveToken({
                token: currentAuth.token,
                user: profileResponse.data.data
              });
            }
            // Refresh the user context with the updated data
            await refreshUser();
          }
        } catch (fetchError) {
          console.error('Error fetching updated profile:', fetchError);
          // Still refresh user context to trigger re-render
          await refreshUser();
        }

        Alert.alert(
          'Address Updated',
          'Your address has been updated successfully.',
          [
            {
              text: 'OK',
              onPress: () => setModalVisible(false)
            }
          ]
        );
        setModalVisible(false)
      } else {
        Alert.alert(
          'Update Failed',
          response.error || 'Failed to update address. Please try again.'
        );
      }
    } catch (error) {
      console.error('Error saving address:', error);
      Alert.alert(
        'Error',
        'There was an error saving the address. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Shipping Addresses</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {addresses.map(address => (
          <View
            key={address.id}
            style={[
              styles.addressCard,
              { backgroundColor: colors.cardBackground }
            ]}
          >
            {address.isDefault && (
              <View style={[styles.defaultBadge, { backgroundColor: colors.primary }]}>
                <Text style={styles.defaultBadgeText}>Default</Text>
              </View>
            )}

            <View style={styles.addressContent}>
              <Text style={[styles.addressName, { color: colors.text }]}>
                {address.name}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.street}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.city}, {address.state} {address.zipCode}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.country}
              </Text>
              <Text style={[styles.addressText, { color: colors.text }]}>
                {address.phone}
              </Text>
            </View>

            <View style={styles.addressActions}>
              <TouchableOpacity
                style={[styles.addressAction, styles.singleAction]}
                onPress={() => handleEditAddress(address)}
              >
                <Ionicons name="create-outline" size={18} color={colors.primary} />
                <Text style={[styles.addressActionText, { color: colors.primary }]}>
                  Edit Address
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        >
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Edit Address
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScrollView}>
              <Input
                label="Street Address"
                placeholder="Enter street address"
                value={street}
                onChangeText={setStreet}
              />

              <Input
                label="City"
                placeholder="Enter city"
                value={city}
                onChangeText={setCity}
              />

              <View style={styles.rowInputs}>
                <View style={styles.halfInput}>
                  <Input
                    label="State/Province"
                    placeholder="Enter state"
                    value={state}
                    onChangeText={setState}
                  />
                </View>

                <View style={styles.halfInput}>
                  <Input
                    label="ZIP Code"
                    placeholder="Enter ZIP code"
                    value={zipCode}
                    onChangeText={setZipCode}
                    keyboardType="numeric"
                  />
                </View>
              </View>

              <Input
                label="Country"
                placeholder="Enter country"
                value={country}
                onChangeText={setCountry}
              />

              <Button
                title="Update Address"
                onPress={handleSaveAddress}
                isLoading={isLoading}
                fullWidth
                style={styles.saveButton}
              />
            </ScrollView>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: Layout.spacing.md,
    paddingBottom: Layout.spacing.xl,
  },
  addressCard: {
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  defaultBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 2,
    borderBottomLeftRadius: Layout.borderRadius.md,
    zIndex: 1,
  },
  defaultBadgeText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
  },
  addressContent: {
    padding: Layout.spacing.md,
  },
  addressName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    marginBottom: 2,
  },
  addressActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  addressAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Layout.spacing.sm,
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
  },
  singleAction: {
    borderRightWidth: 0,
  },
  addressActionText: {
    fontSize: 14,
    marginLeft: 4,
  },
  addAddressButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  addAddressText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: Layout.spacing.xs,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  modalScrollView: {
    padding: Layout.spacing.md,
  },
  rowInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfInput: {
    width: '48%',
  },
  defaultCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Layout.spacing.md,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    marginRight: Layout.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxLabel: {
    fontSize: 14,
  },
  saveButton: {
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
});
