/**
 * Search screen for the CashyMall mobile app
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  useColorScheme,
  Keyboard,
  Platform,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { debounce } from 'lodash';
import AsyncStorage from '@react-native-async-storage/async-storage';

import Header from '../components/Header';
import ProductCard from '../components/ProductCard';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import { searchProducts } from '../services/api/product';
import { addToCart, checkItemExistsInCart } from '../services/api/cart';
import { Product } from '../types/product';
import { useCartWishlist } from '../context/CartWishlistContext';

// Key for storing recent searches in AsyncStorage
const RECENT_SEARCHES_KEY = 'cashymall_recent_searches';
// Maximum number of recent searches to store
const MAX_RECENT_SEARCHES = 5;

export default function SearchScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Use context for real-time cart updates
  const { addToCartCount } = useCartWishlist();

  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRecent, setShowRecent] = useState(true);

  // Load recent searches from AsyncStorage on component mount
  useEffect(() => {
    loadRecentSearches();
  }, []);

  // Load recent searches from AsyncStorage
  const loadRecentSearches = async () => {
    try {
      const storedSearches = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
      if (storedSearches) {
        setRecentSearches(JSON.parse(storedSearches));
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  // Save a search query to recent searches
  const saveSearchQuery = async (query: string) => {
    if (!query.trim()) return;

    try {
      // Add the new query to the beginning of the array and remove duplicates
      const updatedSearches = [
        query,
        ...recentSearches.filter(item => item !== query)
      ].slice(0, MAX_RECENT_SEARCHES);

      setRecentSearches(updatedSearches);
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  };

  // Remove a search query from recent searches
  const removeSearchQuery = async (query: string) => {
    try {
      const updatedSearches = recentSearches.filter(item => item !== query);
      setRecentSearches(updatedSearches);
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error removing recent search:', error);
    }
  };

  // Clear all recent searches
  const clearRecentSearches = async () => {
    try {
      setRecentSearches([]);
      await AsyncStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('Error clearing recent searches:', error);
    }
  };

  // Perform search with a query
  const performSearch = async (query: string) => {
    setLoading(true);
    setError(null);
    setShowRecent(false);

    try {
      const response = await searchProducts(query);
      console.log('Search response:', JSON.stringify(response, null, 2));

      // Based on the API response format, products are in response.data.data
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        if (response.data.data.length > 0) {
          setProducts(response.data.data);
        } else {
          // Empty array means no products found
          setError('No products found');
          setProducts([]);
        }
      } else if (response.data && Array.isArray(response.data)) {
        // Handle case where data might be directly in response.data
        if (response.data.length > 0) {
          setProducts(response.data);
        } else {
          setError('No products found');
          setProducts([]);
        }
      } else {
        console.log('Unexpected data format:', response.data);
        setError('No products found');
        setProducts([]);
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('An error occurred while searching');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search function to prevent too many API calls
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setProducts([]);
        setShowRecent(true);
        return;
      }

      performSearch(query);
    }, 500),
    []
  );

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Handle search submission
  const handleSearchSubmit = () => {
    if (searchQuery.trim()) {
      Keyboard.dismiss();
      saveSearchQuery(searchQuery);
      debouncedSearch.cancel();
      performSearch(searchQuery);
    }
  };

  // Handle selecting a recent search
  const handleRecentSearchSelect = (query: string) => {
    setSearchQuery(query);
    performSearch(query);
  };

  // Handle back navigation
  const handleBackNavigation = () => {
    try {
      router.back();
    } catch (error) {
      // If there's no screen to go back to, navigate to home
      router.push('/(tabs)');
    }
  };

  // Handle add to cart
  const handleAddToCart = async (productId: string) => {
    try {
      // Check if item already exists in cart (without size/color since this is basic add to cart)
      const itemExists = await checkItemExistsInCart(productId);

      if (itemExists) {
        // Show "already in cart" message and don't add the item
        Alert.alert('Already in Cart', 'This item is already in your cart.');
        return;
      }

      const response = await addToCart(productId);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately
      addToCartCount(1);
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header
        title="Search"
        showBack={true}
        showCart={true}
        showSearch={false}
      />

      <View style={styles.searchContainer}>
        <View style={[styles.searchInputContainer, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}>
          <Ionicons name="search-outline" size={20} color={colors.tabIconDefault} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search products..."
            placeholderTextColor={colors.tabIconDefault}
            value={searchQuery}
            onChangeText={handleSearchChange}
            returnKeyType="search"
            autoFocus={true}
            onSubmitEditing={handleSearchSubmit}
          />
          {searchQuery !== '' && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.tabIconDefault} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Recent searches */}
      {showRecent && recentSearches.length > 0 && (
        <View style={styles.recentSearchesContainer}>
          <View style={styles.recentSearchesHeader}>
            <Text style={[styles.recentSearchesTitle, { color: colors.text }]}>Recent Searches</Text>
            <TouchableOpacity onPress={clearRecentSearches}>
              <Text style={[styles.clearText, { color: colors.primary }]}>Clear All</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={recentSearches}
            keyExtractor={(item, index) => `recent-${index}`}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.recentSearchItem}
                onPress={() => handleRecentSearchSelect(item)}
              >
                <View style={styles.recentSearchItemContent}>
                  <Ionicons name="time-outline" size={18} color={colors.tabIconDefault} />
                  <Text style={[styles.recentSearchText, { color: colors.text }]}>{item}</Text>
                </View>
                <TouchableOpacity onPress={() => removeSearchQuery(item)}>
                  <Ionicons name="close" size={18} color={colors.tabIconDefault} />
                </TouchableOpacity>
              </TouchableOpacity>
            )}
          />
        </View>
      )}

      {/* Loading indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      )}

      {/* Error message */}
      {error && !loading && (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.text }]}>{error}</Text>
        </View>
      )}

      {/* Search results */}
      {!loading && !error && products.length > 0 && (
        <FlatList
          data={products}
          renderItem={({ item }) => (
            <ProductCard
              id={item.id.toString()}
              name={item.name}
              price={item.price || 0}
              discountPrice={item.discountPrice}
              imageUrl={item.imageUrl || 'https://via.placeholder.com/300'}
              sizes={item.sizes}
              colors={item.colors}
              isNew={false}
              onAddToCart={() => handleAddToCart(item.id.toString())}
            />
          )}
          keyExtractor={item => item.id.toString()}
          numColumns={2}
          contentContainerStyle={styles.productsGrid}
          columnWrapperStyle={styles.productRow}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* No results */}
      {!loading && !error && !showRecent && products.length === 0 && searchQuery.trim() !== '' && (
        <View style={styles.noResultsContainer}>
          <Ionicons name="search-outline" size={48} color={colors.tabIconDefault} />
          <Text style={[styles.noResultsText, { color: colors.text }]}>
            No products found for "{searchQuery}"
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    padding: Layout.spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.sm,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: Layout.spacing.xs,
    height: '100%',
  },
  recentSearchesContainer: {
    padding: Layout.spacing.md,
    paddingTop: 0,
  },
  recentSearchesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  recentSearchesTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearText: {
    fontSize: 14,
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  recentSearchItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentSearchText: {
    marginLeft: Layout.spacing.sm,
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.lg,
  },
  errorText: {
    marginTop: Layout.spacing.md,
    fontSize: 16,
    textAlign: 'center',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.lg,
  },
  noResultsText: {
    marginTop: Layout.spacing.md,
    fontSize: 16,
    textAlign: 'center',
  },
  productsGrid: {
    padding: Layout.spacing.sm,
  },
  productRow: {
    justifyContent: 'space-between',
  },
});
