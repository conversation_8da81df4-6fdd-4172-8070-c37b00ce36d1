import React, { useContext, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Input from '../../components/Input';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { AuthContext } from '@/context/AuthContext';
import { updateProfile, updateMyProfile, updateMyProfileAlt, getUserProfile } from '../../services/api/auth';
import { saveToken, getToken } from '../../services/storage/tokenStorage';

// Mock user data


export default function EditProfileScreen() {
      const { user, refreshUser } = useContext(AuthContext)!;

const userData = {
  name: `${user?.firstname} ${user?.lastname}`,
  firstName: user?.firstname || '',
  lastName: user?.lastname || '',
  email: user?.email || '',
  phone: user?.phoneNumber || '',
  avatar: user?.profilePicture || '',
};

console.log('Edit Profile - Current user from context:', user);
console.log('Edit Profile - Prepared userData:', userData);

  const [firstName, setFirstName] = useState(userData.firstName);
  const [lastName, setLastName] = useState(userData.lastName);
  const [email, setEmail] = useState(userData.email);
  const [phone, setPhone] = useState(userData.phone);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
  }>({});


  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  // Monitor user context changes and update form fields
  useEffect(() => {
    if (user) {
      console.log('User context changed, updating form fields:', user);
      setFirstName(user.firstname || '');
      setLastName(user.lastname || '');
      setEmail(user.email || '');
      setPhone(user.phoneNumber || '');
    }
  }, [user]);

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;

    // Validate first name
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }

    // Validate last name
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }

    // Validate email
    if (!email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
      isValid = false;
    }

    // Validate phone
    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleUpdateProfile = async () => {
    if (!validateForm()) {
      return;
    }

    if (!user?.id) {
      Alert.alert('Error', 'User information not available. Please try logging in again.');
      return;
    }

    setIsLoading(true);

    try {
      // Prepare user data for API call - try multiple field name formats
      const updatedUserData = {
        id: user.id,
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        firstname: firstName.trim(), // Also include lowercase versions
        lastname: lastName.trim(),   // in case API expects these
        email: email.trim(),
        phoneNumber: phone.trim(),
      };

      // Also try without the ID in the body (like address update)
      const updatedUserDataWithoutId = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        firstname: firstName.trim(),
        lastname: lastName.trim(),
        email: email.trim(),
        phoneNumber: phone.trim(),
      };

      console.log('Current user data:', user);
      console.log('Sending update data:', updatedUserData);

      // Try the first approach (with ID in URL)
      console.log('=== TRYING FIRST APPROACH ===');
      console.log('API Endpoint will be:', `https://api.cashymall.com/api/v1/users/update/${user.id}`);
      console.log('Request method: PATCH');
      console.log('Request body:', JSON.stringify(updatedUserData, null, 2));

      let response = await updateProfile(updatedUserData);
      console.log('First approach - Update response status:', response.status);
      console.log('First approach - Update response data:', response.data);
      console.log('First approach - Update response error:', response.error);

      // If first approach fails or returns 200 but no actual update, try second approach
      if (response.status !== 200 || (response.status === 200 && !response.data)) {
        console.log('=== TRYING SECOND APPROACH ===');
        console.log('API Endpoint will be:', `https://api.cashymall.com/api/v1/users/update`);
        console.log('Request method: PATCH');
        console.log('Request body:', JSON.stringify(updatedUserDataWithoutId, null, 2));

        response = await updateMyProfile(updatedUserDataWithoutId);
        console.log('Second approach - Update response status:', response.status);
        console.log('Second approach - Update response data:', response.data);
        console.log('Second approach - Update response error:', response.error);

        // If second approach also fails, try third approach
        if (response.status !== 200 || (response.status === 200 && !response.data)) {
          console.log('=== TRYING THIRD APPROACH ===');
          console.log('API Endpoint will be:', `https://api.cashymall.com/api/v1/users/update-my-profile`);
          console.log('Request method: PATCH');
          console.log('Request body:', JSON.stringify(updatedUserDataWithoutId, null, 2));

          response = await updateMyProfileAlt(updatedUserDataWithoutId);
          console.log('Third approach - Update response status:', response.status);
          console.log('Third approach - Update response data:', response.data);
          console.log('Third approach - Update response error:', response.error);
        }
      }

      if (response.status === 200) {
        // Fetch the updated user profile from the API to ensure we have the latest data
        try {
          const profileResponse = await getUserProfile(user.id);
          console.log('Fresh profile data:', profileResponse);
          console.log('Fresh profile data.data:', profileResponse.data?.data);
          console.log('Fresh profile status:', profileResponse.status);
          if (profileResponse.data) {
            // Update storage with fresh data from API
            const currentAuth = await getToken();
            if (currentAuth) {
              // Check if the user data is nested under 'data' property
              const userData = profileResponse.data.data || profileResponse.data;
              console.log('Saving user data:', userData);
              await saveToken({
                token: currentAuth.token,
                user: userData
              });
            }
            // Refresh the user context with the updated data
            await refreshUser();
            console.log('User context refreshed');
          } else {
            // Fallback: update with local data if API fetch fails
            const currentAuth = await getToken();
            if (currentAuth) {
              const updatedUser = {
                ...user,
                ...updatedUserData,
                // Also update the lowercase versions for compatibility
                firstname: firstName.trim(),
                lastname: lastName.trim(),
              };
              console.log('Saving fallback user data:', updatedUser);
              await saveToken({
                token: currentAuth.token,
                user: updatedUser
              });
              await refreshUser();
              console.log('Fallback user context refreshed');
            }
          }
        } catch (fetchError) {
          console.error('Error fetching updated profile:', fetchError);
          // Fallback: update with local data
          const currentAuth = await getToken();
          if (currentAuth) {
            const updatedUser = {
              ...user,
              ...updatedUserData,
              // Also update the lowercase versions for compatibility
              firstname: firstName.trim(),
              lastname: lastName.trim(),
            };
            console.log('Saving error fallback user data:', updatedUser);
            await saveToken({
              token: currentAuth.token,
              user: updatedUser
            });
            await refreshUser();
            console.log('Error fallback user context refreshed');
          }
        }

        Alert.alert(
          'Profile Updated',
          'Your profile has been updated successfully.',
          [
            {
              text: 'OK',
              onPress: () => router.back()
            }
          ]
        );
        router.back()
      } else {
        Alert.alert(
          'Update Failed',
          response.error || 'Failed to update profile. Please try again.'
        );
      }
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert(
        'Error',
        'There was an error updating your profile. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangeAvatar = () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose an option',
      [
        {
          text: 'Take Photo',
          onPress: () => handleImagePicker('camera'),
        },
        {
          text: 'Choose from Gallery',
          onPress: () => handleImagePicker('gallery'),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleImagePicker = async (_source: 'camera' | 'gallery') => {
    if (!user?.id) {
      Alert.alert('Error', 'User information not available.');
      return;
    }

    try {
      // Note: This is a placeholder for image picker implementation
      // You would need to implement actual image picking using expo-image-picker
      // For now, we'll show a message that this feature needs to be implemented
      Alert.alert(
        'Feature Coming Soon',
        'Profile picture upload will be available in the next update. Please use the web version for now.',
        [{ text: 'OK' }]
      );

      // Example of how the API call would work:
      // const imageUri = 'selected_image_uri_from_picker';
      // const response = await uploadProfilePicture(user.id, imageUri);
      // if (response.data) {
      //   // Update user data with new profile picture
      //   const currentAuth = await getToken();
      //   if (currentAuth) {
      //     const updatedUser = { ...user, profilePicture: response.data.profilePicture };
      //     await saveToken({
      //       token: currentAuth.token,
      //       user: updatedUser
      //     });
      //   }
      // }
    } catch (error) {
      console.error('Profile picture upload error:', error);
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Edit Profile</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.avatarContainer}>
          <Image
            source={{ uri: userData.avatar }}
            style={styles.avatar}
          />
          <TouchableOpacity
            style={[styles.editAvatarButton, { backgroundColor: colors.primary }]}
            onPress={handleChangeAvatar}
          >
            <Ionicons name="camera" size={16} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <View style={styles.formContainer}>
          <Input
            label="First Name"
            placeholder="Enter your first name"
            autoCapitalize="words"
            value={firstName}
            onChangeText={setFirstName}
            error={errors.firstName}
            leftIcon={<Ionicons name="person-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Input
            label="Last Name"
            placeholder="Enter your last name"
            autoCapitalize="words"
            value={lastName}
            onChangeText={setLastName}
            error={errors.lastName}
            leftIcon={<Ionicons name="person-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Input
            label="Email Address"
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
            error={errors.email}
            leftIcon={<Ionicons name="mail-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Input
            label="Phone Number"
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
            value={phone}
            onChangeText={setPhone}
            error={errors.phone}
            leftIcon={<Ionicons name="call-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Button
            title="Update Profile"
            onPress={handleUpdateProfile}
            isLoading={isLoading}
            fullWidth
            style={styles.updateButton}
          />

          <TouchableOpacity
            style={styles.changePasswordButton}
            onPress={() => router.push('/profile/change-password')}
          >
            <Text style={[styles.changePasswordText, { color: colors.primary }]}>
              Change Password
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: Layout.spacing.lg,
    position: 'relative',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: '35%',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    paddingHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
  updateButton: {
    marginTop: Layout.spacing.md,
  },
  changePasswordButton: {
    alignSelf: 'center',
    marginTop: Layout.spacing.lg,
    padding: Layout.spacing.sm,
  },
  changePasswordText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
