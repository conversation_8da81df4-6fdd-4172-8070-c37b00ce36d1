import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Input from '../../components/Input';
import Button from '../../components/Button';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { updatePassword } from '../../services/api/auth';

export default function ChangePasswordScreen() {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  }>({});

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const validateForm = () => {
    const newErrors: typeof errors = {};
    let isValid = true;

    // Validate current password
    if (!currentPassword.trim()) {
      newErrors.currentPassword = 'Current password is required';
      isValid = false;
    }

    // Validate new password
    if (!newPassword.trim()) {
      newErrors.newPassword = 'New password is required';
      isValid = false;
    } else if (newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
      isValid = false;
    }

    // Validate confirm password
    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your new password';
      isValid = false;
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    // Check if new password is different from current
    if (currentPassword === newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Call the update password API
      const response = await updatePassword(currentPassword, newPassword);

      if (response.data) {
        Alert.alert(
          'Password Changed',
          'Your password has been changed successfully.',
          [
            {
              text: 'OK',
              onPress: () => router.push('/(tabs)/profile')
            }
          ]
        );
        router.push('/(tabs)/profile')
      } else {
        Alert.alert(
          'Change Failed',
          response.error || 'Failed to change password. Please check your current password and try again.'
        );
      }
    } catch (error) {
      console.error('Password change error:', error);
      Alert.alert(
        'Error',
        'There was an error changing your password. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Change Password</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formContainer}>
          <Text style={[styles.description, { color: colors.text }]}>
            Enter your current password and choose a new password.
          </Text>

          <Input
            label="Current Password"
            placeholder="Enter your current password"

            isPassword
            value={currentPassword}
            onChangeText={setCurrentPassword}
            error={errors.currentPassword}
            leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Input
            label="New Password"
            placeholder="Enter new password"

            isPassword
            value={newPassword}
            onChangeText={setNewPassword}
            error={errors.newPassword}
            leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Input
            label="Confirm New Password"
            placeholder="Confirm new password"

            isPassword
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            error={errors.confirmPassword}
            leftIcon={<Ionicons name="lock-closed-outline" size={20} color={colors.tabIconDefault} />}
          />

          <Button
            title="Change Password"
            onPress={handleChangePassword}
            isLoading={isLoading}
            fullWidth
            style={styles.changeButton}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    paddingHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.xl,
  },
  description: {
    fontSize: 16,
    marginBottom: Layout.spacing.lg,
    textAlign: 'center',
    lineHeight: 22,
  },
  changeButton: {
    marginTop: Layout.spacing.md,
  },
});
