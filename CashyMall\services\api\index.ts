/**
 * API client for the CashyMall mobile app
 */
import { getToken, refreshAccessToken } from '../storage/tokenStorage';
import { API_BASE_URL } from './endpoints';

// HTTP request methods
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API request options
interface ApiRequestOptions {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  requiresAuth?: boolean;
  skipRefreshToken?: boolean;
}

// API response interface
interface ApiResponse<T = any> {
  data: T | null;
  error: string | null;
  status: number;
}

/**
 * Make an API request
 * @param endpoint API endpoint
 * @param options Request options
 * @returns API response
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    headers = {},
    body,
    requiresAuth = true,
    skipRefreshToken = false,
  } = options;

  // Prepare request headers
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers,
  };

  // Add authorization header if required
  if (requiresAuth) {
    const authData = await getToken();
    if (authData) {
      requestHeaders['Authorization'] = `Bearer ${authData.token}`;
    } else if (!skipRefreshToken) {
      // If no token is available and we're not skipping token refresh,
      // try to refresh the token
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        const newAuthData = await getToken();
        if (newAuthData) {
          requestHeaders['Authorization'] = `Bearer ${newAuthData.token}`;
        }
      } else {
        // If token refresh fails, return an authentication error
        return {
          data: null,
          error: 'Authentication required',
          status: 401,
        };
      }
    }
  }

  // Prepare request options
  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
  };

  // Add request body if provided
  if (body) {
    requestOptions.body = JSON.stringify(body);
  }

  try {
    // Debug logging for profile update requests
    if (endpoint.includes('/users/update')) {
      console.log('=== API REQUEST DEBUG ===');
      console.log('Endpoint:', endpoint);
      console.log('Method:', method);
      console.log('Headers:', requestHeaders);
      console.log('Body:', body);
      console.log('========================');
    }

    // Make the API request
    const response = await fetch(endpoint, requestOptions);
    const status = response.status;

    // Handle 401 Unauthorized error (token expired)
    if (status === 401 && requiresAuth && !skipRefreshToken) {
      // Try to refresh the token
      const refreshed = await refreshAccessToken();
      if (refreshed) {
        // Retry the request with the new token
        return apiRequest(endpoint, {
          ...options,
          skipRefreshToken: true, // Prevent infinite loop
        });
      } else {
        // If token refresh fails, return an authentication error
        return {
          data: null,
          error: 'Authentication required',
          status: 401,
        };
      }
    }

    // Parse response data
    let data = null;
    let error = null;

    if (status !== 204) { // No content
      try {
        data = await response.json();
      } catch (e) {
        // If response is not JSON, set error
        error = 'Invalid response format';
      }
    }

    // Handle error responses
    if (!response.ok) {
      error = data?.message || `Request failed with status ${status}`;
      data = null;
    }

    // Debug logging for profile update responses
    if (endpoint.includes('/users/update')) {
      console.log('=== API RESPONSE DEBUG ===');
      console.log('Status:', status);
      console.log('Response OK:', response.ok);
      console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
      console.log('Data:', data);
      console.log('Error:', error);
      console.log('Raw Response Text (if available):', response.statusText);
      console.log('==========================');
    }

    return { data, error, status };
  } catch (error: any) {
    // Handle network errors
    return {
      data: null,
      error: error.message || 'Network error',
      status: 0,
    };
  }
}

/**
 * GET request helper
 */
export function get<T = any>(
  endpoint: string,
  options: Omit<ApiRequestOptions, 'method' | 'body'> = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, { ...options, method: 'GET' });
}

/**
 * POST request helper
 */
export function post<T = any>(
  endpoint: string,
  body: any,
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, { ...options, method: 'POST', body });
}

/**
 * PUT request helper
 */
export function put<T = any>(
  endpoint: string,
  body: any,
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, { ...options, method: 'PUT', body });
}

/**
 * DELETE request helper
 */
export function del<T = any>(
  endpoint: string,
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, { ...options, method: 'DELETE' });
}

/**
 * PATCH request helper
 */
export function patch<T = any>(
  endpoint: string,
  body: any,
  options: Omit<ApiRequestOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, { ...options, method: 'PATCH', body });
}

export default {
  get,
  post,
  put,
  delete: del,
  patch,
};
