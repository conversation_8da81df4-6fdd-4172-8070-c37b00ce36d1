import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { getWishlists } from '../services/api/wishlist';
import { getCart } from '../services/api/cart';

interface CartWishlistContextType {
  // Wishlist
  wishlistCount: number;
  setWishlistCount: (count: number) => void;
  incrementWishlistCount: () => void;
  decrementWishlistCount: () => void;
  refreshWishlistCount: () => Promise<void>;

  // Wishlist change notifications
  notifyWishlistChange: (productId: string, isInWishlist: boolean) => void;
  subscribeToWishlistChanges: (callback: (productId: string, isInWishlist: boolean) => void) => () => void;

  // Cart
  cartCount: number;
  setCartCount: (count: number) => void;
  incrementCartCount: () => void;
  decrementCartCount: () => void;
  addToCartCount: (quantity?: number) => void;
  refreshCartCount: () => Promise<void>;

  // Loading states
  wishlistLoading: boolean;
  cartLoading: boolean;
}

const CartWishlistContext = createContext<CartWishlistContextType | undefined>(undefined);

export const useCartWishlist = () => {
  const context = useContext(CartWishlistContext);
  if (context === undefined) {
    throw new Error('useCartWishlist must be used within a CartWishlistProvider');
  }
  return context;
};

interface CartWishlistProviderProps {
  children: React.ReactNode;
}

export const CartWishlistProvider: React.FC<CartWishlistProviderProps> = ({ children }) => {
  const [wishlistCount, setWishlistCount] = useState<number>(0);
  const [cartCount, setCartCount] = useState<number>(0);
  const [wishlistLoading, setWishlistLoading] = useState<boolean>(false);
  const [cartLoading, setCartLoading] = useState<boolean>(false);

  // Wishlist change notification system
  const [wishlistChangeListeners, setWishlistChangeListeners] = useState<Set<(productId: string, isInWishlist: boolean) => void>>(new Set());

  // Wishlist functions
  const refreshWishlistCount = useCallback(async () => {
    try {
      setWishlistLoading(true);
      const response = await getWishlists();
      console.log('Context: Wishlist Response:', response);

      if (response && response.data && response.data.products) {
        const count = response.data.products.length;
        setWishlistCount(count);
        console.log('Context: Wishlist count updated to:', count);
      } else {
        setWishlistCount(0);
      }
    } catch (error) {
      console.error('Context: Error fetching wishlist count:', error);
      setWishlistCount(0);
    } finally {
      setWishlistLoading(false);
    }
  }, []);

  const incrementWishlistCount = useCallback(() => {
    setWishlistCount(prev => {
      const newCount = prev + 1;
      console.log('Context: Wishlist count incremented to:', newCount);
      return newCount;
    });
  }, []);

  const decrementWishlistCount = useCallback(() => {
    setWishlistCount(prev => {
      const newCount = Math.max(0, prev - 1);
      console.log('Context: Wishlist count decremented to:', newCount);
      return newCount;
    });
  }, []);

  // Wishlist change notification functions
  const notifyWishlistChange = useCallback((productId: string, isInWishlist: boolean) => {
    console.log(`Context: Notifying wishlist change for product ${productId}: ${isInWishlist}`);
    wishlistChangeListeners.forEach(listener => {
      try {
        listener(productId, isInWishlist);
      } catch (error) {
        console.error('Error in wishlist change listener:', error);
      }
    });
  }, [wishlistChangeListeners]);

  const subscribeToWishlistChanges = useCallback((callback: (productId: string, isInWishlist: boolean) => void) => {
    console.log('Context: New wishlist change listener subscribed');
    setWishlistChangeListeners(prev => new Set([...prev, callback]));

    // Return unsubscribe function
    return () => {
      console.log('Context: Wishlist change listener unsubscribed');
      setWishlistChangeListeners(prev => {
        const newSet = new Set(prev);
        newSet.delete(callback);
        return newSet;
      });
    };
  }, []);

  // Cart functions
  const refreshCartCount = useCallback(async () => {
    try {
      setCartLoading(true);
      const response = await getCart();
      console.log('Context: Cart Response:', response);

      if (response && response.data && typeof response.data.itemCount === 'number') {
        setCartCount(response.data.itemCount);
        console.log('Context: Cart count updated to:', response.data.itemCount);
      } else if (response && response.data && Array.isArray(response.data.items)) {
        // Fallback: count items manually if itemCount is not available
        const count = response.data.items.length;
        setCartCount(count);
        console.log('Context: Cart count (fallback) updated to:', count);
      } else {
        setCartCount(0);
      }
    } catch (error) {
      console.error('Context: Error fetching cart count:', error);
      setCartCount(0);
    } finally {
      setCartLoading(false);
    }
  }, []);

  const incrementCartCount = useCallback(() => {
    setCartCount(prev => {
      const newCount = prev + 1;
      console.log('Context: Cart count incremented to:', newCount);
      return newCount;
    });
  }, []);

  const decrementCartCount = useCallback(() => {
    setCartCount(prev => {
      const newCount = Math.max(0, prev - 1);
      console.log('Context: Cart count decremented to:', newCount);
      return newCount;
    });
  }, []);

  const addToCartCount = useCallback((quantity: number = 1) => {
    setCartCount(prev => {
      const newCount = prev + quantity;
      console.log(`Context: Cart count increased by ${quantity} to:`, newCount);
      return newCount;
    });
  }, []);

  // Initial load
  useEffect(() => {
    refreshWishlistCount();
    refreshCartCount();
  }, [refreshWishlistCount, refreshCartCount]);

  const value: CartWishlistContextType = {
    // Wishlist
    wishlistCount,
    setWishlistCount,
    incrementWishlistCount,
    decrementWishlistCount,
    refreshWishlistCount,

    // Wishlist change notifications
    notifyWishlistChange,
    subscribeToWishlistChanges,

    // Cart
    cartCount,
    setCartCount,
    incrementCartCount,
    decrementCartCount,
    addToCartCount,
    refreshCartCount,

    // Loading states
    wishlistLoading,
    cartLoading,
  };

  return (
    <CartWishlistContext.Provider value={value}>
      {children}
    </CartWishlistContext.Provider>
  );
};
