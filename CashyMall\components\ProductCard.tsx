import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import { checkWishlist, addToWishlist, removeFromWishlist } from '@/services/api/wishlist';
import { useCartWishlist } from '../context/CartWishlistContext';
import ProductSelectionModal from './ProductSelectionModal';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  discountPrice?: number;
  imageUrl: string;
  sizes?: string | null;
  colors?: string;
  isNew?: boolean;
  onAddToCart?: () => void;
  onAddToWishlist?: () => void;
  onWishlistChange?: (productId: string, isInWishlist: boolean) => void;
}

export default function ProductCard({
  id,
  name,
  price,
  discountPrice,
  imageUrl,
  sizes,
  colors,
  isNew = false,
  onAddToCart,
  // onAddToWishlist,
  onWishlistChange,
}: ProductCardProps) {
  const colorScheme = useColorScheme() || 'light';
  const colorTheme = Colors[colorScheme];

  // Use context for real-time updates
  const {
    incrementWishlistCount,
    decrementWishlistCount,
    notifyWishlistChange,
    subscribeToWishlistChanges
  } = useCartWishlist();

  const [isInWishlist, setIsInWishlist] = useState<boolean>(false);
  const [isWishlistLoading, setIsWishlistLoading] = useState<boolean>(false);
  const [showSelectionModal, setShowSelectionModal] = useState<boolean>(false);

useEffect(() => {
  const checkIfInWishlist = async () => {
    try {
      console.log(`Initial wishlist check for product ${id}`);
      const result = await checkWishlist(id);
      console.log("Initial wishlist check response:", result);

      // Handle the API response structure consistently
      let isInList = false;
      if (result && result.error === null && result.data !== null) {
        isInList = !!result.data;
      }

      console.log(`Initial wishlist status for product ${id}: ${isInList}`);
      setIsInWishlist(isInList);
    } catch (err) {
      console.error("Failed to check initial wishlist status", err);
      setIsInWishlist(false); // Default to false on error
    }
  };

  checkIfInWishlist();
}, [id]);

// Subscribe to global wishlist changes
useEffect(() => {
  const unsubscribe = subscribeToWishlistChanges((productId: string, isInWishlist: boolean) => {
    if (productId === id) {
      console.log(`ProductCard ${id}: Received global wishlist change notification: ${isInWishlist}`);
      setIsInWishlist(isInWishlist);
    }
  });

  // Cleanup subscription on unmount
  return unsubscribe;
}, [id, subscribeToWishlistChanges]);

const handleWishlistToggle = async () => {
  // Prevent multiple simultaneous requests
  if (isWishlistLoading) return;

  // Store the current state before making changes
  const previousState = isInWishlist;

  try {
    setIsWishlistLoading(true);
    console.log(`Toggling wishlist for product ${id}, current state: ${previousState}`);

    // Optimistically update UI first for better user experience
    setIsInWishlist(!previousState);

    if (previousState) {
      // Remove from wishlist (was in wishlist)
      console.log(`Removing product ${id} from wishlist...`);
      const response = await removeFromWishlist(id);
      console.log("Remove response:", response);

      // Update context count immediately
      decrementWishlistCount();
    } else {
      // Add to wishlist (was not in wishlist)
      console.log(`Adding product ${id} to wishlist...`);
      const response = await addToWishlist(id);
      console.log("Add response:", response);

      // Update context count immediately
      incrementWishlistCount();
    }

    // Verify the current status after the operation
    console.log(`Verifying wishlist status for product ${id}...`);
    const verifyStatus = await checkWishlist(id);
    console.log("Verify response:", verifyStatus);

    // Handle the API response structure
    let actualStatus = false;
    if (verifyStatus && verifyStatus.error === null && verifyStatus.data !== null) {
      actualStatus = !!verifyStatus.data;
    }

    console.log(`Final wishlist status for product ${id}: ${actualStatus}`);
    setIsInWishlist(actualStatus);

    // Notify global context about wishlist change
    notifyWishlistChange(id, actualStatus);

    // Notify parent component about wishlist change
    if (onWishlistChange) {
      console.log(`ProductCard: Calling onWishlistChange callback for product ${id} with status ${actualStatus}`);
      onWishlistChange(id, actualStatus);
    } else {
      console.log(`ProductCard: No onWishlistChange callback provided for product ${id}`);
    }

    // Call the original callback if provided
    // onAddToWishlist && onAddToWishlist();
  } catch (err) {
    console.error("Failed to toggle wishlist status:", err);
    // Revert the optimistic update on error
    setIsInWishlist(previousState);
  } finally {
    setIsWishlistLoading(false);
  }
};
  // Ensure price is a number and handle null/undefined values
  const safePrice = typeof price === 'number' ? price : 0;
  const safeDiscountPrice = typeof discountPrice === 'number' ? discountPrice : null;

  // Check if there's a valid discount
  const hasDiscount = safeDiscountPrice !== null && safeDiscountPrice < safePrice;

  // Check if product has selectable options
  const hasSelectableOptions = (sizes && sizes.trim().length > 0) || (colors && colors.trim().length > 0);

  const handleAddToCartClick = () => {
    if (hasSelectableOptions) {
      // Product has sizes/colors, show selection modal
      setShowSelectionModal(true);
    } else {
      // Product has no sizes/colors, use direct add to cart
      onAddToCart && onAddToCart();
    }
  };

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colorTheme.cardBackground
  };

  const badgeStyle = {
    ...styles.badge,
    backgroundColor: colorTheme.primary
  };

   const discountStyle = {
    ...styles.badgeDiscount,
    backgroundColor: colorTheme.primary
  };
  const cartButtonStyle = {
    ...styles.cartButton,
    backgroundColor: colorTheme.primary
  };

  const cartButtonWishStyle = {
    ...styles.cartButtonwish,
    backgroundColor: colorTheme.primary
  }

  const nameStyle = {
    ...styles.name,
    color: colorTheme.text
  };

  const discountPriceStyle = {
    ...styles.discountPrice,
    color: colorTheme.primary
  };

  const originalPriceStyle = {
    ...styles.originalPrice,
    color: colorTheme.tabIconDefault
  };

  const priceStyle = {
    ...styles.price,
    color: colorTheme.text
  };

  const router = useRouter();

  const handleProductPress = () => {
    console.log(`ProductCard: Navigating to product ${id}`);
    try {
      router.push(`/product/${id}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
      <View style={containerStyle}>
        <TouchableOpacity style={styles.imageContainer} onPress={handleProductPress}>
          <Image
            source={{ uri: imageUrl }}
            style={styles.image}
            resizeMode="cover"
          />
          {isNew && (
            <View style={badgeStyle}>
              <Text style={styles.badgeText}>NEW</Text>
            </View>
          )}
           {hasDiscount && (
            <View style={discountStyle}>
              <Text style={styles.badgeDiscountText}>${safeDiscountPrice}</Text>
            </View>
          )}
          <TouchableOpacity
            style={cartButtonStyle}
            onPress={(e) => {
              e.stopPropagation();
              handleAddToCartClick();
            }}
          >
            <Ionicons name="cart-outline" size={18} color="#ffffff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[cartButtonWishStyle, { opacity: isWishlistLoading ? 0.6 : 1 }]}
            onPress={(e) => {
              e.stopPropagation();
              handleWishlistToggle();
            }}
            disabled={isWishlistLoading}
          >
              <Ionicons
    name={isInWishlist ? "heart" : "heart-outline"}
    size={18}
    color={isInWishlist ? "red" : "#ffffff"}
  />
          </TouchableOpacity>
        </TouchableOpacity>

        <TouchableOpacity style={styles.infoContainer} onPress={handleProductPress}>
          <Text
            style={nameStyle}
            numberOfLines={2}
          >
            {name}
          </Text>

          <View style={styles.priceContainer}>
            {hasDiscount ? (
              <>
                <Text style={discountPriceStyle}>
                  ${(safePrice-( safePrice * safeDiscountPrice /100)).toFixed(2)}
                </Text>
                <Text style={originalPriceStyle}>
                  ${safePrice.toFixed(2)}
                </Text>
              </>
            ) : (
              <Text style={priceStyle}>
                ${safePrice.toFixed(2)}
              </Text>
            )}
          </View>
        </TouchableOpacity>

        {/* Product Selection Modal - Only show for products with selectable options */}
        {hasSelectableOptions && (
          <ProductSelectionModal
            visible={showSelectionModal}
            onClose={() => setShowSelectionModal(false)}
            product={{
              id,
              name,
              price,
              discountPrice,
              imageUrl,
              sizes,
              colors
            }}
            onSuccess={() => {
              // Item already added to cart by ProductSelectionModal
              // No need to call onAddToCart again to avoid duplicate additions
            }}
          />
        )}
      </View>

  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
    width: (Layout.window.width - (Layout.spacing.md * 3)) / 2, // 2 columns with spacing
    marginBottom: Layout.spacing.md,
  },
  imageContainer: {
    position: 'relative',
    height: 180,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  badge: {
    position: 'absolute',
    top: Layout.spacing.xs,
    left: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },

   badgeDiscount: {
    position: 'absolute',
    top: Layout.spacing.xs,
    right: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  badgeDiscountText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cartButton: {
    position: 'absolute',
    bottom: Layout.spacing.xs,
    right: Layout.spacing.xs,
    width: 32,
    height: 32,
    borderRadius: Layout.borderRadius.round,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartButtonwish: {
        position: 'absolute',
    bottom: Layout.spacing.xs,
    left: Layout.spacing.xs,
    width: 32,
    height: 32,
    borderRadius: Layout.borderRadius.round,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoContainer: {
    padding: Layout.spacing.sm,
  },
  name: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  discountPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: Layout.spacing.xs,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
});
