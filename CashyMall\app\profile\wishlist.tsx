import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  useColorScheme,
  FlatList,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { addToCart, checkItemExistsInCart } from '../../services/api/cart';
import { clearWishlist, getWishlists } from '@/services/api/wishlist';
import { Wishlist } from '@/types/product';
import ProductCard from '@/components/ProductCard';



export default function WishlistScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const [wishlistItems, setWishlistItems] = useState<Wishlist[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchWishlist = async () => {
      try {
        const response = await getWishlists() ;
        console.log('Wishlist Response:', response.data.products);
        if (response && response.data) {
          setWishlistItems( response.data.products);
          console.log('Wishlist Items:', response.data.products);
        }
      } catch (error) {
        console.error('Failed to load wishlist:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, []);

  const handleAddToCart = async (id:string) => {
    try {
      // Check if item already exists in cart (without size/color since this is basic add to cart)
      const itemExists = await checkItemExistsInCart(id);

      if (itemExists) {
        // Show "already in cart" message and don't add the item
        Alert.alert('Already in Cart', 'This item is already in your cart.');
        return;
      }

      const response = await addToCart(id);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const handleWishlistChange = (productId: string, isInWishlist: boolean) => {
    console.log(`Wishlist change callback: productId=${productId}, isInWishlist=${isInWishlist}`);
    if (!isInWishlist) {
      // Item was removed from wishlist, update local state
      console.log(`Removing product ${productId} from wishlist display`);
      setWishlistItems(prev => {
        const newItems = prev.filter(item => item.id.toString() !== productId);
        console.log(`Wishlist items count: ${prev.length} -> ${newItems.length}`);
        console.log(`Filtered out item with id: ${productId}`);
        return newItems;
      });
    }
  };
const clearList = async () => {
    try {
      const response = await clearWishlist();
      if (response.error) {
        Alert.alert('Error', 'Failed to clear wishlist');
        return;
      }
      setWishlistItems([]);
    } catch (err) {
      console.error('Error clearing wishlist:', err);
      Alert.alert('Error', 'Failed to clear wishlist');
    }
  };


  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={{ color: colors.text }}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Wishlist</Text>
        <View style={styles.placeholder} />
         <TouchableOpacity
                  style={styles.clearButton}
                  onPress={() => clearList()}
                >
                  <Ionicons name="trash-outline"  size={20} color={colors.error} />
                  <Text style={[styles.headerTitle, { color: colors.text }]}>Clear</Text>
                </TouchableOpacity>
      </View>

      {wishlistItems.length > 0 ? (
        <FlatList
          data={wishlistItems}
          numColumns={2}
         renderItem={({ item }) => (
                   <ProductCard
                     id={item.id.toString()}
                     name={item.name}
                     price={item.price}
                     discountPrice={item.discountPrice || undefined}
                     imageUrl={item.imageUrl}
                     sizes={undefined}
                     colors={undefined}
                    //  isNew={item.tags?.some(tag => tag.name === 'New Arrival')}
                     onAddToCart={() => handleAddToCart(item.id)}
                     onWishlistChange={handleWishlistChange}
                   />
                 )}
                 keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.wishlistContainer}
          columnWrapperStyle={styles.productRow}
          showsVerticalScrollIndicator={true}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={64} color={colors.tabIconDefault} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>Your Wishlist is Empty</Text>
          <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
            Save items you like by tapping the heart icon on products
          </Text>
          <TouchableOpacity
            style={[styles.shopButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/shop')}
          >
            <Text style={styles.shopButtonText}>Start Shopping</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  clearButton: {
    padding: Layout.spacing.xs,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  wishlistContainer: {
    padding: Layout.spacing.md,
  },
   productRow: {
    justifyContent: 'space-between',
  },

  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.lg,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xs,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  shopButton: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  shopButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});
