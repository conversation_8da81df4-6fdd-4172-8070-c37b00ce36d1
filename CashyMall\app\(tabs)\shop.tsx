import { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  useColorScheme,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import ProductCard from '../../components/ProductCard';
import FilterIconsModal from '../../../CashyMall/components/FilterIconsModal';
import SpecificFilterModal from '../../../CashyMall/components/SpecificFilterModal';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getProducts, getRootCategories, searchProducts } from '../../services/api/product';
import { addToCart, checkItemExistsInCart } from '../../services/api/cart';
import { Product, Category } from '../../types/product';
import { useCartWishlist } from '../../context/CartWishlistContext';

// Sort options
const sortOptions = [
  { id: 'newest', name: 'Newest' },
  { id: 'price-low', name: 'Price: Low to High' },
  { id: 'price-high', name: 'Price: High to Low' },
  { id: 'discount', name: 'Discount' }
];

export default function ShopScreen() {
  const params = useLocalSearchParams();
  const initialCategory = typeof params.category === 'string' ? params.category : 'all';
  const initialQuery = typeof params.query === 'string' ? params.query : '';

  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [selectedSort, setSelectedSort] = useState('newest');
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [showFilterIcons, setShowFilterIcons] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [showSpecificFilter, setShowSpecificFilter] = useState(false);
  const [filterValues, setFilterValues] = useState<{[key: string]: any}>({});
  const [products, setProducts] = useState<Product[]>([]);
  const [availableSizes, setAvailableSizes] = useState<string[]>([]);
  const [availableColors, setAvailableColors] = useState<string[]>([]);
  const [maxProductPrice, setMaxProductPrice] = useState<number>(1000);
  const [loading, setLoading] = useState(true);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Use context for real-time updates
  const { addToCartCount } = useCartWishlist();

  // Centralized function to fetch categories
  const fetchCategories = useCallback(async () => {
    setLoadingCategories(true);
    try {
      const response = await getRootCategories();
      console.log('Categories Response:', JSON.stringify(response, null, 2));

      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // Filter out inactive categories and add "All" category
        const activeCategories = response.data.data.filter(category => category.active);
        setCategories(activeCategories);
      } else {
        console.error('Invalid categories data format:', response);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
    } finally {
      setLoadingCategories(false);
    }
  }, []);

  // Centralized function to fetch products based on search query and selected category
  const fetchProducts = useCallback(async () => {
    setLoading(true);
    try {
      let response;

      if (searchQuery) {
        // If there's a search query, use the search endpoint
        response = await searchProducts(searchQuery);
      } else if (selectedCategory !== 'all' && selectedCategory !== '') {
        // If a category is selected, filter by category
        const categoryId = parseInt(selectedCategory, 10);
        if (!isNaN(categoryId)) {
          response = await searchProducts({
            categoryId: categoryId
          });
        } else {
          // Fallback to all products if category ID is not a number
          response = await getProducts();
        }
      } else {
        // Otherwise, get all products
        response = await getProducts();
      }

      console.log('Products Response:', JSON.stringify(response, null, 2));

      if (response.data && Array.isArray(response.data)) {
        // If response.data is already an array, use it directly
        setProducts(response.data);
        const maxPrice = extractFiltersFromProducts(response.data);
        setMaxProductPrice(maxPrice);
        setError(null);
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // If response.data.data is an array (nested data structure)
        setProducts(response.data.data);
        const maxPrice = extractFiltersFromProducts(response.data.data);
        setMaxProductPrice(maxPrice);
        setError(null);
      } else {
        setError('Invalid product data format received from API');
        console.error('Invalid product data format:', response);
      }
    } catch (err) {
      setError('An error occurred while fetching products');
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedCategory]);

  // Initial data fetch on mount
  useEffect(() => {
    fetchCategories();
    fetchProducts();
  }, [fetchCategories, fetchProducts]);

  // Refresh data when search query or category changes
  useEffect(() => {
    fetchProducts();
  }, [searchQuery, selectedCategory, fetchProducts]);

  // Refresh data when screen comes into focus (when tab is opened)
  useFocusEffect(
    useCallback(() => {
      console.log('Shop screen focused - refreshing data');
      fetchCategories();
      fetchProducts();
    }, [fetchCategories, fetchProducts])
  );

  // Filter products by category, search query, and applied filters
  const filteredProducts = products.filter(product => {
    // Filter by category
    const matchesCategory = selectedCategory === 'all' ||
      (product.categoryId && product.categoryId.toString() === selectedCategory);

    // Filter by search query
    const matchesSearch = searchQuery === '' ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by price range
    const priceFilter = filterValues.price;
    const matchesPrice = !priceFilter ||
      ((product.discountPrice || product.price) >= priceFilter[0] &&
       (product.discountPrice || product.price) <= priceFilter[1]);

    // Filter by rating
    const ratingFilter = filterValues.rating;
    const matchesRating = !ratingFilter ||
      (product.averageRating && product.averageRating >= ratingFilter);

    // Filter by discount
    const discountFilter = filterValues.discount;
    const hasDiscount = product.discountPrice !== null && product.discountPrice < product.price;
    const discountPercentage = hasDiscount && product.discountPrice !== null
      ? Math.round(((product.price - product.discountPrice) / product.price) * 100)
      : 0;
    const matchesDiscount = !discountFilter || !Array.isArray(discountFilter) || discountFilter.length === 0 ||
      (Array.isArray(discountFilter) && discountFilter.some((minDiscount: string) => {
        const minValue = parseInt(minDiscount, 10);
        return discountPercentage >= minValue;
      }));

    // Filter by size
    const sizeFilter = filterValues.size;
    const matchesSize = !sizeFilter || !Array.isArray(sizeFilter) || sizeFilter.length === 0 || !product.sizes ||
      (product.sizes && Array.isArray(sizeFilter) && sizeFilter.some((size: string) =>
        product.sizes?.split(',').map(s => s.trim()).includes(size)
      ));

    // Filter by color
    const colorFilter = filterValues.color;
    const matchesColor = !colorFilter || !Array.isArray(colorFilter) || colorFilter.length === 0 || !product.colors ||
      (product.colors && Array.isArray(colorFilter) && colorFilter.some((color: string) =>
        product.colors?.toLowerCase().split(',').map(c => c.trim()).includes(color.toLowerCase())
      ));

    // Filter by shipping
    const shippingFilter = filterValues.shipping;
    const matchesShipping = !shippingFilter ||
                           typeof shippingFilter !== 'object' ||
                           Object.keys(shippingFilter).length === 0 ||
                           !(shippingFilter as any).free;

    return matchesCategory && matchesSearch && matchesPrice &&
           matchesRating && matchesDiscount && matchesSize &&
           matchesColor && matchesShipping;
  });

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (selectedSort) {
      case 'price-low':
        return (a.discountPrice || a.price) - (b.discountPrice || b.price);
      case 'price-high':
        return (b.discountPrice || b.price) - (a.discountPrice || a.price);
      case 'discount':
        const aDiscount = a.discountPrice ? a.price - a.discountPrice : 0;
        const bDiscount = b.discountPrice ? b.price - b.discountPrice : 0;
        return bDiscount - aDiscount;
      case 'newest':
      default:
        // Sort by creation date if available, otherwise by featured status
        if (a.createdAt && b.createdAt) {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
        return a.featured ? -1 : b.featured ? 1 : 0;
    }
  });

  const handleAddToCart = async (productId: string) => {
    try {
      // Check if item already exists in cart (without size/color since this is basic add to cart)
      const itemExists = await checkItemExistsInCart(productId);

      if (itemExists) {
        // Show "already in cart" message and don't add the item
        Alert.alert('Already in Cart', 'This item is already in your cart.');
        return;
      }

      const response = await addToCart(productId);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately
      addToCartCount(1);
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };


  // Extract available sizes, colors, and max price from products
  const extractFiltersFromProducts = (products: Product[]) => {
    const sizesSet = new Set<string>();
    const colorsSet = new Set<string>();
    let maxProductPrice = 0;

    products.forEach(product => {
      // Extract sizes
      if (product.sizes) {
        const sizeArray = product.sizes.split(',').map(size => size.trim());
        sizeArray.forEach(size => sizesSet.add(size));
      }

      // Extract colors
      if (product.colors) {
        const colorArray = product.colors.split(',').map(color => color.trim());
        colorArray.forEach(color => colorsSet.add(color));
      }

      // Find max price
      const productPrice = product.price || 0;
      if (productPrice > maxProductPrice) {
        maxProductPrice = productPrice;
      }
    });

    // Round up max price to nearest 100 for better UX
    const roundedMaxPrice = Math.ceil(maxProductPrice / 100) * 100;
    const finalMaxPrice = roundedMaxPrice > 0 ? roundedMaxPrice : 1000; // Fallback to 1000 if no products or prices

    setAvailableSizes(Array.from(sizesSet));
    setAvailableColors(Array.from(colorsSet));

    console.log('Available sizes:', Array.from(sizesSet));
    console.log('Available colors:', Array.from(colorsSet));
    console.log('Max product price:', finalMaxPrice);

    return finalMaxPrice;
  };

  const handleFilterSelect = (filterId: string) => {
    setSelectedFilter(filterId);
    console.log(`Selected filter: ${filterId}`);

    // Show the specific filter modal for the selected filter
    setShowSpecificFilter(true);
  };

  const handleApplySpecificFilter = (filterId: string, values: any) => {
    console.log(`Applied filter: ${filterId} with values:`, values);

    // Save the filter values
    setFilterValues(prev => ({
      ...prev,
      [filterId]: values
    }));

    // Close the specific filter modal
    setShowSpecificFilter(false);

    // The actual filtering is done in the filteredProducts variable
    // which is automatically recalculated when filterValues changes
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    console.log('Clearing all filters');
    setFilterValues({});
    setSelectedCategory('all');
    setSelectedSort('newest');
    setSearchQuery('');

  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Shop" />

      <View style={styles.searchContainer}>
        <View style={[styles.searchInputContainer, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}>
          <Ionicons name="search-outline" size={20} color={colors.tabIconDefault} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search products..."
            placeholderTextColor={colors.tabIconDefault}
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
            onSubmitEditing={() => {
              // Trigger search when the user presses the search button on the keyboard
              console.log('Searching for:', searchQuery);
            }}
          />
          {searchQuery !== '' && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.tabIconDefault} />
            </TouchableOpacity>
          )}
        </View>

        {/* <TouchableOpacity
          style={[styles.filterButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="options-outline" size={20} color="#ffffff" />
        </TouchableOpacity> */}

        <TouchableOpacity
          style={[styles.filterButton, { backgroundColor: colors.secondary }]}
          onPress={() => setShowFilterIcons(true)}
        >
          <Ionicons name="options-outline" size={20} color="#ffffff" />
        </TouchableOpacity>


      </View>

      {/* Active Filters Indicator */}
      {Object.keys(filterValues).length > 0 && (
        <View style={[styles.activeFiltersContainer, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.activeFiltersText, { color: colors.text }]}>
            {Object.keys(filterValues).length} active {Object.keys(filterValues).length === 1 ? 'filter' : 'filters'}
          </Text>
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={handleClearFilters}
          >
            <Text style={[styles.clearFiltersText, { color: colors.primary }]}>Clear All</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Filter Icons Modal */}
      <FilterIconsModal
        visible={showFilterIcons}
        onClose={() => setShowFilterIcons(false)}
        onSelectFilter={handleFilterSelect}
      />

      {/* Specific Filter Modal */}
      <SpecificFilterModal
        visible={showSpecificFilter}
        onClose={() => setShowSpecificFilter(false)}
        filterId={selectedFilter}
        onApplyFilter={handleApplySpecificFilter}
        availableSizes={availableSizes}
        availableColors={availableColors}
        maxPrice={maxProductPrice}
      />


        <View style={styles.filtersContainer}>
          <Text style={[styles.filtersTitle, { color: colors.text }]}>Categories</Text>
          {loadingCategories ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContainer}
            >
              {/* Add "All" category at the beginning */}
              <TouchableOpacity
                key="all"
                style={[
                  styles.categoryButton,
                  selectedCategory === 'all' && { backgroundColor: colors.primary }
                ]}
                onPress={() => setSelectedCategory('all')}
              >
                <Text
                  style={[
                    styles.categoryButtonText,
                    selectedCategory === 'all' ? { color: '#ffffff' } : { color: colors.text }
                  ]}
                >
                  All
                </Text>
              </TouchableOpacity>

              {categories.map(category => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category.id.toString() && { backgroundColor: colors.primary }
                  ]}
                  onPress={() => setSelectedCategory(category.id.toString())}
                >
                  <Text
                    style={[
                      styles.categoryButtonText,
                      selectedCategory === category.id.toString() ? { color: '#ffffff' } : { color: colors.text }
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}

          <Text style={[styles.filtersTitle, { color: colors.text }]}>Sort By</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.sortContainer}
          >
            {sortOptions.map(option => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.sortButton,
                  selectedSort === option.id && { backgroundColor: colors.primary }
                ]}
                onPress={() => setSelectedSort(option.id)}
              >
                <Text
                  style={[
                    styles.sortButtonText,
                    selectedSort === option.id ? { color: '#ffffff' } : { color: colors.text }
                  ]}
                >
                  {option.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>


      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading products...
          </Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.text }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setError(null);
              fetchCategories();
              fetchProducts();
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={sortedProducts}
          renderItem={({ item }) => (
            <ProductCard
              id={item.id.toString()}
              name={item.name}
              price={item.price}
              discountPrice={item.discountPrice || undefined}
              imageUrl={item.imageUrl}
              sizes={item.sizes}
              colors={item.colors}
              isNew={item.tags?.some(tag => tag.name === 'New Arrival')}
              onAddToCart={() => handleAddToCart(item.id.toString())}
            />
          )}
          keyExtractor={item => item.id.toString()}
          numColumns={2}
          contentContainerStyle={styles.productsGrid}
          columnWrapperStyle={styles.productRow}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="search" size={48} color={colors.tabIconDefault} />
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No products found
              </Text>
            </View>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    padding: Layout.spacing.md,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.sm,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: Layout.spacing.xs,
    height: '100%',
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Layout.spacing.sm,
  },
  filtersContainer: {
    padding: Layout.spacing.md,
    paddingTop: 0,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: Layout.spacing.xs,
  },
  categoriesContainer: {
    paddingBottom: Layout.spacing.md,
  },
  categoryButton: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.sm,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    paddingBottom: Layout.spacing.md,
  },
  sortButton: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.sm,
  },
  sortButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsGrid: {
    padding: Layout.spacing.md,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  errorText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.lg,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
  },
  activeFiltersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  activeFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },
  clearFiltersButton: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '600',
  },
});


