/**
 * Authentication context for the CashyMall mobile app
 */
import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { Alert } from 'react-native';
import { useRouter, useSegments } from 'expo-router';
import * as authApi from '../services/api/auth';
import { User } from '../services/api/auth';
import { isAuthenticated, clearTokens, getUser } from '../services/storage/tokenStorage';

// Authentication context interface
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isSignedIn: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signUp: (userData: SignupData) => Promise<boolean>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

// Signup data interface
interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
}

// Create authentication context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Authentication provider props
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Authentication provider component
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSignedIn, setIsSignedIn] = useState(false);

  const router = useRouter();
  const segments = useSegments();

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Handle navigation based on authentication status
  useEffect(() => {
    if (!isLoading) {
      const inAuthGroup = segments[0] === 'auth';

      if (!isSignedIn && !inAuthGroup) {
        // Redirect to login if not signed in and not in auth group
        router.replace('/auth/login');
      } else if (isSignedIn && inAuthGroup) {
        // Redirect to home if signed in and in auth group
        router.replace('/(tabs)');
      }
    }
  }, [isSignedIn, segments, isLoading]);

  /**
   * Check if user is authenticated
   */
  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      const authenticated = await isAuthenticated();

      if (authenticated) {
        // Get user data from storage
        const userData = await getUser();

        if (userData) {
          setUser(userData);
          setIsSignedIn(true);
        } else {
          // Clear tokens if user data is not available
          await clearTokens();
          setIsSignedIn(false);
        }
      } else {
        setIsSignedIn(false);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setIsSignedIn(false);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sign in user
   * @param email User email
   * @param password User password
   * @returns True if sign in successful, false otherwise
   */
  const signIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      const response = await authApi.login({ email, password });

      if (response.data && response.data.token) {
        setUser(response.data.user);
        setIsSignedIn(true);
        return true;
      } else {
        Alert.alert('Login Failed', response.error || response.data?.message || 'Invalid email or password');
        return false;
      }
    } catch (error) {
      console.error('Sign in error:', error);
      Alert.alert('Login Error', 'An unexpected error occurred. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sign up user
   * @param userData User registration data
   * @returns True if sign up successful, false otherwise
   */
  const signUp = async (userData: SignupData): Promise<boolean> => {
    try {
      setIsLoading(true);

      const response = await authApi.signup(userData);

      if (response.data && response.data.token) {
        setUser(response.data.user);
        setIsSignedIn(true);
        return true;
      } else {
        Alert.alert('Registration Failed', response.error || response.data?.message || 'Could not create account');
        return false;
      }
    } catch (error) {
      console.error('Sign up error:', error);
      Alert.alert('Registration Error', 'An unexpected error occurred. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sign out user
   */
  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Clear tokens from local storage (no API call needed)
      await clearTokens();

      // Clear user data and state
      setUser(null);
      setIsSignedIn(false);

    } catch (error) {
      console.error('Sign out error:', error);

      // Even if token clearing fails, clear the state
      setUser(null);
      setIsSignedIn(false);

    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Refresh user data from storage
   */
  const refreshUser = async (): Promise<void> => {
    try {
      const userData = await getUser();
      console.log('RefreshUser - Retrieved user data from storage:', userData);
      if (userData) {
        setUser(userData);
        console.log('RefreshUser - User state updated successfully');
      } else {
        console.log('RefreshUser - No user data found in storage');
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  // Context value
  const value: AuthContextType = {
    user,
    isLoading,
    isSignedIn,
    signIn,
    signUp,
    signOut,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Hook to use authentication context
 * @returns Authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
}
